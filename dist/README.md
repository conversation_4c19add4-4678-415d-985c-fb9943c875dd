# 构建产物目录

本目录用于存放所有构建生成的文件和分发包。

## 📁 目录结构

```
dist/
├── OFD Reader.app/        # macOS 应用包
├── archives/              # 历史版本归档
│   ├── v1.0.0/
│   ├── v1.0.1/
│   └── ...
└── README.md             # 本文件
```

## 🎯 文件说明

### `OFD Reader.app/`
**类型**: macOS 应用包
**用途**: 可直接运行的 macOS 应用程序
**生成方式**: 通过 `make build` 或 `./scripts/build_app.sh` 生成

**内容结构**:
```
OFD Reader.app/
├── Contents/
│   ├── Info.plist         # 应用配置
│   ├── MacOS/
│   │   └── ofdx-reader    # 可执行文件
│   └── Resources/
│       └── AppIcon.icns   # 应用图标
```

### `archives/`
**用途**: 存放不同版本的构建产物
**管理方式**: 按版本号组织，便于版本回退和对比

## 🔧 管理命令

### 构建新版本
```bash
# 构建当前版本
make build

# 构建并验证
make build && make verify
```

### 版本归档
```bash
# 手动归档当前版本
./scripts/archive_version.sh v1.0.0

# 或使用 make 命令
make archive VERSION=v1.0.0
```

### 清理构建产物
```bash
# 清理当前构建
make clean

# 清理所有历史版本（谨慎使用）
rm -rf dist/archives/*
```

## 📦 分发准备

### 应用签名（可选）
```bash
# 如果有开发者证书，可以签名应用
codesign --force --deep --sign "Developer ID Application: Your Name" "dist/OFD Reader.app"
```

### 创建 DMG 安装包
```bash
# 创建 DMG 文件用于分发
./scripts/create_dmg.sh
```

### 压缩分发
```bash
# 创建 ZIP 压缩包
cd dist
zip -r "OFD Reader v1.0.0.zip" "OFD Reader.app"
```

## 🚀 安装和分发

### 本地安装
```bash
# 安装到 Applications 目录
make install

# 或手动复制
cp -R "dist/OFD Reader.app" /Applications/
```

### 用户分发
1. **直接分发**: 将 `OFD Reader.app` 打包为 ZIP 文件
2. **DMG 安装包**: 创建专业的 macOS 安装包
3. **App Store**: 通过 App Store Connect 分发（需要开发者账号）

## 📋 注意事项

### 版本控制
- ✅ **应该提交**: `dist/README.md`
- ❌ **不应提交**: `dist/OFD Reader.app/`（已在 .gitignore 中排除）
- ❌ **不应提交**: `dist/archives/`（构建产物）

### 文件权限
确保应用包具有正确的执行权限：
```bash
chmod +x "dist/OFD Reader.app/Contents/MacOS/ofdx-reader"
```

### 系统兼容性
- **最低系统要求**: macOS 10.15 (Catalina)
- **推荐系统**: macOS 11.0 (Big Sur) 或更高
- **架构支持**: x86_64, arm64 (Apple Silicon)

## 🔍 故障排除

### 应用无法启动
1. 检查文件权限
2. 验证应用包结构完整性
3. 查看系统日志获取错误信息

### 安装失败
1. 确保有足够的磁盘空间
2. 检查 Applications 目录权限
3. 尝试手动拖拽安装

### 版本冲突
1. 卸载旧版本应用
2. 清理应用数据缓存
3. 重新安装新版本

## 📚 相关脚本

- `scripts/build_app.sh` - 构建应用包
- `scripts/verify_icon.sh` - 验证构建结果
- `scripts/archive_version.sh` - 版本归档（待创建）
- `scripts/create_dmg.sh` - 创建 DMG 安装包（待创建）
