<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="docGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="512" cy="512" r="480" fill="url(#docGradient)" stroke="#2c3e50" stroke-width="8"/>
  
  <!-- 文档主体 -->
  <rect x="256" y="180" width="512" height="664" rx="32" ry="32" fill="#ffffff" stroke="#bdc3c7" stroke-width="4"/>
  
  <!-- 文档头部 -->
  <rect x="256" y="180" width="512" height="120" rx="32" ry="32" fill="url(#headerGradient)"/>
  <rect x="256" y="268" width="512" height="32" fill="url(#headerGradient)"/>
  
  <!-- OFD 文字 -->
  <text x="512" y="260" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="#ffffff">OFD</text>
  
  <!-- 文档内容线条 -->
  <rect x="320" y="360" width="384" height="16" rx="8" fill="#2c3e50"/>
  <rect x="320" y="400" width="320" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="432" width="360" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="464" width="280" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="496" width="340" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="528" width="300" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="560" width="380" height="12" rx="6" fill="#7f8c8d"/>
  <rect x="320" y="592" width="260" height="12" rx="6" fill="#7f8c8d"/>
  
  <!-- 阅读图标（眼睛） -->
  <ellipse cx="650" cy="720" rx="80" ry="50" fill="#e67e22" stroke="#d35400" stroke-width="4"/>
  <ellipse cx="650" cy="720" rx="40" ry="30" fill="#ffffff"/>
  <circle cx="650" cy="720" r="20" fill="#3498db"/>
  <circle cx="660" cy="710" r="8" fill="#ffffff"/>
</svg>
