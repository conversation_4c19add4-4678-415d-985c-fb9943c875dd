<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主背景渐变 - 水滴玻璃效果 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#5856D6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AF52DE;stop-opacity:1" />
    </linearGradient>
    
    <!-- 玻璃高光效果 -->
    <linearGradient id="glassHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>
    
    <!-- 文档渐变 -->
    <linearGradient id="docGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.9" />
    </linearGradient>
    
    <!-- 头部渐变 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF3B30;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9500;stop-opacity:1" />
    </linearGradient>
    
    <!-- 内阴影滤镜 -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- macOS 圆角蒙版 -->
    <clipPath id="macOSMask">
      <rect x="0" y="0" width="1024" height="1024" rx="229" ry="229"/>
    </clipPath>
  </defs>
  
  <!-- 完全填充的背景 - 方形无圆角 -->
  <rect x="0" y="0" width="1024" height="1024" 
        fill="url(#mainGradient)"/>
  
  <!-- 应用 macOS 蒙版到所有内容 -->
  <g clip-path="url(#macOSMask)">
    
    <!-- 玻璃高光层 -->
    <rect x="0" y="0" width="1024" height="1024" 
          fill="url(#glassHighlight)"/>
    
    <!-- 文档主体 -->
    <rect x="180" y="120" width="664" height="784" rx="45" ry="45" 
          fill="url(#docGradient)" 
          filter="url(#innerShadow)"
          stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    
    <!-- 文档头部 -->
    <rect x="180" y="120" width="664" height="150" rx="45" ry="45" 
          fill="url(#headerGradient)"/>
    <rect x="180" y="225" width="664" height="45" 
          fill="url(#headerGradient)"/>
    
    <!-- OFD 文字 -->
    <text x="512" y="220" 
          font-family="-apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif" 
          font-size="90" 
          font-weight="700" 
          text-anchor="middle" 
          fill="#ffffff"
          style="letter-spacing: 3px;">OFD</text>
    
    <!-- 文档内容线条 -->
    <rect x="260" y="330" width="504" height="22" rx="11" fill="rgba(0,0,0,0.8)"/>
    <rect x="260" y="375" width="420" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="410" width="460" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="445" width="380" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="480" width="440" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="515" width="400" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="550" width="480" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="585" width="360" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="620" width="420" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    <rect x="260" y="655" width="340" height="18" rx="9" fill="rgba(0,0,0,0.5)"/>
    
    <!-- 现代化阅读图标 -->
    <g transform="translate(650, 750)">
      <!-- 眼睛外框 -->
      <ellipse cx="0" cy="0" rx="95" ry="60" 
               fill="rgba(255,255,255,0.95)" 
               stroke="rgba(0,122,255,0.8)" 
               stroke-width="3"/>
      
      <!-- 眼球 -->
      <circle cx="0" cy="0" r="38" fill="#007AFF"/>
      
      <!-- 瞳孔 -->
      <circle cx="0" cy="0" r="20" fill="#000000"/>
      
      <!-- 高光点 -->
      <circle cx="8" cy="-8" r="7" fill="#ffffff" opacity="0.9"/>
      <circle cx="-4" cy="4" r="3" fill="#ffffff" opacity="0.6"/>
    </g>
    
    <!-- 顶部玻璃反光效果 -->
    <ellipse cx="512" cy="180" rx="320" ry="85" 
             fill="rgba(255,255,255,0.25)" 
             opacity="0.7"/>
    
    <!-- 底部微妙渐变 -->
    <defs>
      <linearGradient id="bottomGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color:#000000;stop-opacity:0" />
        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.05" />
      </linearGradient>
    </defs>
    <rect x="0" y="800" width="1024" height="224" 
          fill="url(#bottomGradient)"/>
  </g>
</svg>
