#!/bin/bash

# 设置变量
APP_NAME="OFD Reader"
BUNDLE_NAME="OFD Reader.app"
EXECUTABLE_NAME="ofdx-reader"

echo "开始构建 macOS .app 包..."

# 编译 Release 版本
echo "编译 Release 版本..."
cargo build --release

# 检查编译是否成功
if [ ! -f "target/release/$EXECUTABLE_NAME" ]; then
    echo "错误: 编译失败，找不到可执行文件"
    exit 1
fi

# 创建 .app 包目录结构
echo "创建 .app 包目录结构..."
rm -rf "$BUNDLE_NAME"
mkdir -p "$BUNDLE_NAME/Contents/MacOS"
mkdir -p "$BUNDLE_NAME/Contents/Resources"

# 复制可执行文件
echo "复制可执行文件..."
cp "target/release/$EXECUTABLE_NAME" "$BUNDLE_NAME/Contents/MacOS/"

# 复制 Info.plist
echo "复制 Info.plist..."
cp "Info.plist" "$BUNDLE_NAME/Contents/"

# 复制图标文件
echo "复制图标文件..."
if [ -f "AppIcon.icns" ]; then
    cp "AppIcon.icns" "$BUNDLE_NAME/Contents/Resources/"
    echo "✅ 图标文件已复制"
else
    echo "⚠️  警告: 找不到 AppIcon.icns 文件"
fi

# 设置可执行权限
chmod +x "$BUNDLE_NAME/Contents/MacOS/$EXECUTABLE_NAME"

echo "✅ macOS .app 包构建完成: $BUNDLE_NAME"
echo "您可以在 Finder 中双击运行应用程序"