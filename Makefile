# OFD Reader Makefile
# 提供便捷的构建和管理命令

.PHONY: help dev build icon clean test verify install run

# 默认目标
help:
	@echo "OFD Reader 构建工具"
	@echo "=================="
	@echo ""
	@echo "可用命令:"
	@echo "  dev      - 开发模式运行"
	@echo "  build    - 构建 macOS 应用包"
	@echo "  icon     - 生成应用图标"
	@echo "  verify   - 验证构建结果"
	@echo "  test     - 运行测试"
	@echo "  clean    - 清理构建文件"
	@echo "  install  - 安装到 Applications 目录"
	@echo "  run      - 运行构建的应用"
	@echo "  help     - 显示此帮助信息"

# 开发模式运行
dev:
	@echo "🚀 启动开发模式..."
	cargo run

# 构建完整应用包
build: icon
	@echo "🏗️ 构建 macOS 应用包..."
	./scripts/build_app.sh

# 生成应用图标
icon:
	@echo "🎨 生成应用图标..."
	./scripts/create_icon.sh

# 验证构建结果
verify:
	@echo "🔍 验证构建结果..."
	./scripts/verify_icon.sh

# 运行测试
test:
	@echo "🧪 运行测试..."
	cargo test

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	cargo clean
	rm -rf "OFD Reader.app"
	rm -rf assets/icons/AppIcon.iconset

# 安装到 Applications 目录
install: build
	@echo "📦 安装到 Applications 目录..."
	@if [ -d "OFD Reader.app" ]; then \
		cp -R "OFD Reader.app" /Applications/; \
		echo "✅ 已安装到 /Applications/OFD Reader.app"; \
	else \
		echo "❌ 未找到应用包，请先运行 make build"; \
		exit 1; \
	fi

# 运行构建的应用
run: build
	@echo "🚀 启动应用..."
	open "OFD Reader.app"

# 完整的构建和测试流程
all: clean icon build verify
	@echo "✅ 完整构建流程完成！"

# 发布前检查
release-check: all test
	@echo "🔍 发布前检查..."
	./scripts/final_icon_test.sh
