#!/bin/bash

# 创建应用图标的脚本 - 符合 macOS 标准
# 需要一个 1024x1024 的 PNG 图片作为源文件

ICON_SOURCE="assets/icons/icon_source.png"  # 您的源图标文件
ICONSET_DIR="assets/icons/AppIcon.iconset"

echo "🎨 开始生成 macOS 应用图标..."

if [ ! -f "$ICON_SOURCE" ]; then
    echo "❌ 请提供一个名为 assets/icons/icon_source.png 的 1024x1024 图标文件"
    exit 1
fi

# 验证源图标尺寸
ICON_SIZE=$(sips -g pixelWidth "$ICON_SOURCE" | tail -1 | awk '{print $2}')
if [ "$ICON_SIZE" != "1024" ]; then
    echo "⚠️  警告: 源图标尺寸不是 1024x1024，当前尺寸: ${ICON_SIZE}x${ICON_SIZE}"
fi

# 创建 iconset 目录
rm -rf "$ICONSET_DIR"
mkdir -p "$ICONSET_DIR"

echo "📐 生成不同尺寸的图标..."

# 生成所有必需的图标尺寸 - 使用高质量缩放
sips -z 16 16     "$ICON_SOURCE" --out "$ICONSET_DIR/icon_16x16.png" > /dev/null
sips -z 32 32     "$ICON_SOURCE" --out "$ICONSET_DIR/<EMAIL>" > /dev/null
sips -z 32 32     "$ICON_SOURCE" --out "$ICONSET_DIR/icon_32x32.png" > /dev/null
sips -z 64 64     "$ICON_SOURCE" --out "$ICONSET_DIR/<EMAIL>" > /dev/null
sips -z 128 128   "$ICON_SOURCE" --out "$ICONSET_DIR/icon_128x128.png" > /dev/null
sips -z 256 256   "$ICON_SOURCE" --out "$ICONSET_DIR/<EMAIL>" > /dev/null
sips -z 256 256   "$ICON_SOURCE" --out "$ICONSET_DIR/icon_256x256.png" > /dev/null
sips -z 512 512   "$ICON_SOURCE" --out "$ICONSET_DIR/<EMAIL>" > /dev/null
sips -z 512 512   "$ICON_SOURCE" --out "$ICONSET_DIR/icon_512x512.png" > /dev/null
sips -z 1024 1024 "$ICON_SOURCE" --out "$ICONSET_DIR/<EMAIL>" > /dev/null

echo "✅ 所有尺寸图标已生成"

# 验证生成的图标
echo "🔍 验证生成的图标文件..."
for icon in "$ICONSET_DIR"/*.png; do
    if [ ! -f "$icon" ]; then
        echo "❌ 图标文件生成失败: $icon"
        exit 1
    fi
done

# 生成 .icns 文件
echo "📦 生成 .icns 文件..."
iconutil -c icns "$ICONSET_DIR" -o "assets/icons/AppIcon.icns"

if [ ! -f "assets/icons/AppIcon.icns" ]; then
    echo "❌ .icns 文件生成失败"
    exit 1
fi

# 显示文件信息
ICNS_SIZE=$(ls -lh assets/icons/AppIcon.icns | awk '{print $5}')
echo "✅ 图标文件 assets/icons/AppIcon.icns 已生成 (大小: $ICNS_SIZE)"

# 清理临时文件
rm -rf "$ICONSET_DIR"

echo "🎉 图标生成完成！"