#!/bin/bash

# 创建 DMG 安装包脚本
# 用法: ./scripts/create_dmg.sh [版本号]

VERSION=${1:-"latest"}
APP_NAME="OFD Reader"
DMG_NAME="$APP_NAME $VERSION"
SOURCE_APP="dist/OFD Reader.app"
DMG_DIR="dist/dmg_temp"
FINAL_DMG="dist/$DMG_NAME.dmg"

echo "📦 创建 DMG 安装包: $DMG_NAME"

# 检查应用包是否存在
if [ ! -d "$SOURCE_APP" ]; then
    echo "❌ 未找到应用包: $SOURCE_APP"
    echo "请先运行: make build"
    exit 1
fi

# 清理并创建临时目录
echo "🧹 准备临时目录..."
rm -rf "$DMG_DIR"
mkdir -p "$DMG_DIR"

# 复制应用到临时目录
echo "📋 复制应用包..."
cp -R "$SOURCE_APP" "$DMG_DIR/"

# 创建 Applications 目录的符号链接
echo "🔗 创建 Applications 链接..."
ln -s /Applications "$DMG_DIR/Applications"

# 创建自定义背景图片（可选）
# 这里可以添加自定义的 DMG 背景图片

# 创建临时 DMG
echo "🔨 创建临时 DMG..."
TEMP_DMG="dist/temp_$DMG_NAME.dmg"
rm -f "$TEMP_DMG"

# 计算需要的大小（应用大小 + 50MB 缓冲）
APP_SIZE=$(du -sm "$SOURCE_APP" | cut -f1)
DMG_SIZE=$((APP_SIZE + 50))

hdiutil create -srcfolder "$DMG_DIR" -volname "$DMG_NAME" -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" -format UDRW -size ${DMG_SIZE}m "$TEMP_DMG"

# 挂载临时 DMG 进行自定义
echo "🎨 自定义 DMG 外观..."
MOUNT_DIR="/Volumes/$DMG_NAME"
hdiutil attach -readwrite -noverify -noautoopen "$TEMP_DMG"

# 等待挂载完成
sleep 2

# 设置 Finder 视图选项
if [ -d "$MOUNT_DIR" ]; then
    echo "⚙️  配置 Finder 视图..."
    
    # 使用 AppleScript 设置 Finder 视图
    osascript << EOF
tell application "Finder"
    tell disk "$DMG_NAME"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 100, 900, 400}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 72
        set background picture of viewOptions to file ".background:background.png"
        make new alias file at container window to POSIX file "/Applications" with properties {name:"Applications"}
        set position of item "$APP_NAME.app" of container window to {150, 200}
        set position of item "Applications" of container window to {350, 200}
        close
        open
        update without registering applications
        delay 2
    end tell
end tell
EOF

    # 同步文件系统
    sync
    sleep 2
fi

# 卸载临时 DMG
echo "📤 卸载临时 DMG..."
hdiutil detach "$MOUNT_DIR" || true

# 创建最终的只读 DMG
echo "🔒 创建最终 DMG..."
rm -f "$FINAL_DMG"
hdiutil convert "$TEMP_DMG" -format UDZO -imagekey zlib-level=9 -o "$FINAL_DMG"

# 清理临时文件
echo "🧹 清理临时文件..."
rm -f "$TEMP_DMG"
rm -rf "$DMG_DIR"

# 显示结果
if [ -f "$FINAL_DMG" ]; then
    DMG_SIZE=$(ls -lh "$FINAL_DMG" | awk '{print $5}')
    echo ""
    echo "✅ DMG 创建完成！"
    echo "📦 文件: $FINAL_DMG ($DMG_SIZE)"
    echo ""
    echo "🚀 测试建议:"
    echo "1. 挂载 DMG: open '$FINAL_DMG'"
    echo "2. 测试拖拽安装"
    echo "3. 验证应用启动正常"
else
    echo "❌ DMG 创建失败"
    exit 1
fi
