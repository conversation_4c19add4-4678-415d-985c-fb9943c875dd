#!/bin/bash

# 版本归档脚本
# 用法: ./scripts/archive_version.sh [版本号]

VERSION=$1

if [ -z "$VERSION" ]; then
    echo "❌ 请提供版本号"
    echo "用法: $0 <版本号>"
    echo "示例: $0 v1.0.0"
    exit 1
fi

echo "📦 归档版本: $VERSION"

# 检查应用包是否存在
if [ ! -d "dist/OFDReader.app" ]; then
    echo "❌ 未找到应用包，请先运行构建"
    echo "运行: make build"
    exit 1
fi

# 创建归档目录
ARCHIVE_DIR="dist/archives/$VERSION"
mkdir -p "$ARCHIVE_DIR"

echo "📁 创建归档目录: $ARCHIVE_DIR"

# 复制应用包
echo "📋 复制应用包..."
cp -R "dist/OFDReader.app" "$ARCHIVE_DIR/"

# 创建版本信息文件
echo "📝 创建版本信息..."
cat > "$ARCHIVE_DIR/VERSION_INFO.txt" << EOF
OFDReader $VERSION
==================

构建时间: $(date)
构建主机: $(hostname)
Git 提交: $(git rev-parse HEAD 2>/dev/null || echo "未知")
Git 分支: $(git branch --show-current 2>/dev/null || echo "未知")

文件列表:
$(find "$ARCHIVE_DIR" -type f -exec basename {} \; | sort)

构建配置:
- Rust 版本: $(rustc --version)
- Cargo 版本: $(cargo --version)
- 系统版本: $(sw_vers -productVersion)
EOF

# 创建 ZIP 压缩包
echo "🗜️  创建压缩包..."
cd "dist/archives"
zip -r "OFDReader $VERSION.zip" "$VERSION/"
cd ../..

# 显示归档信息
ARCHIVE_SIZE=$(du -sh "$ARCHIVE_DIR" | cut -f1)
ZIP_SIZE=$(ls -lh "dist/archives/OFDReader $VERSION.zip" | awk '{print $5}')

echo ""
echo "✅ 版本归档完成！"
echo "📁 归档目录: $ARCHIVE_DIR ($ARCHIVE_SIZE)"
echo "📦 压缩包: dist/archives/OFDReader $VERSION.zip ($ZIP_SIZE)"
echo ""
echo "🚀 分发建议:"
echo "1. 测试归档的应用包: open '$ARCHIVE_DIR/OFDReader.app'"
echo "2. 分发压缩包: dist/archives/OFDReader $VERSION.zip"
echo "3. 创建 Git 标签: git tag $VERSION && git push origin $VERSION"
