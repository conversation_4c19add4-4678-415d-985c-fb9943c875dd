# 构建和工具脚本

本目录包含了 OFDReader 项目的所有构建和工具脚本。

## 📁 脚本列表

### 🏗️ 构建脚本

#### `build_app.sh`
**用途**: 构建完整的 macOS .app 应用包

**功能**:
- 编译 Rust 项目为 Release 版本
- 创建标准的 macOS .app 目录结构
- 复制可执行文件到应用包
- 复制 Info.plist 配置文件
- 复制应用图标文件
- 设置正确的文件权限

**使用方法**:
```bash
# 从项目根目录运行
./scripts/build_app.sh
```

**输出**: `OFDReader.app` - 完整的 macOS 应用包

---

### 🎨 图标相关脚本

#### `create_icon.sh`
**用途**: 从 SVG 源文件生成完整的 macOS 应用图标

**功能**:
- 验证源图标文件尺寸
- 生成所有标准 macOS 图标尺寸 (16x16 到 1024x1024)
- 创建 .icns 格式的图标文件
- 支持 Retina 显示屏的高分辨率图标

**使用方法**:
```bash
# 从项目根目录运行
./scripts/create_icon.sh
```

**前置条件**: 需要 `assets/icons/icon_source.png` (1024x1024)
**输出**: `assets/icons/AppIcon.icns`

#### `verify_icon.sh`
**用途**: 验证应用图标配置和文件完整性

**功能**:
- 检查应用包和图标文件是否存在
- 验证 Info.plist 中的图标配置
- 显示图标文件详细信息
- 检查应用分类配置

**使用方法**:
```bash
# 从项目根目录运行
./scripts/verify_icon.sh
```

#### `test_icon.sh`
**用途**: 测试图标在系统中的显示效果

**功能**:
- 启动应用进行图标测试
- 提供图标问题排查指南
- 刷新系统图标缓存

**使用方法**:
```bash
# 从项目根目录运行
./scripts/test_icon.sh
```

#### `final_icon_test.sh`
**用途**: 最终的图标测试和验证

**功能**:
- 显示当前图标规格信息
- 检查所有相关文件
- 启动应用进行程序坞测试
- 提供详细的检查指南

**使用方法**:
```bash
# 从项目根目录运行
./scripts/final_icon_test.sh
```

---

## 🔄 典型工作流程

### 首次构建
```bash
# 1. 生成图标文件
./scripts/create_icon.sh

# 2. 构建应用包
./scripts/build_app.sh

# 3. 验证图标配置
./scripts/verify_icon.sh

# 4. 测试图标效果
./scripts/final_icon_test.sh
```

### 图标更新流程
```bash
# 1. 更新 assets/icons/icon_source.svg
# 2. 重新生成 PNG: qlmanage -t -s 1024 -o assets/icons assets/icons/icon_source.svg
# 3. 重命名: mv assets/icons/icon_source.svg.png assets/icons/icon_source.png
# 4. 生成新图标
./scripts/create_icon.sh

# 5. 重新构建应用
./scripts/build_app.sh

# 6. 测试新图标
./scripts/final_icon_test.sh
```

### 发布前检查
```bash
# 验证所有配置
./scripts/verify_icon.sh

# 最终测试
./scripts/final_icon_test.sh
```

---

## 📋 注意事项

1. **权限**: 所有脚本都需要执行权限，使用 `chmod +x scripts/*.sh` 设置
2. **路径**: 所有脚本都应该从项目根目录运行
3. **依赖**: 需要 macOS 系统工具: `sips`, `iconutil`, `qlmanage`
4. **图标源文件**: 确保 `assets/icons/icon_source.png` 是 1024x1024 的高质量图像

---

## 🛠️ 故障排除

### 图标不显示
1. 运行 `./scripts/verify_icon.sh` 检查配置
2. 清除图标缓存: `killall Dock && killall Finder`
3. 重启系统（最彻底的解决方案）

### 构建失败
1. 检查 Rust 工具链是否正确安装
2. 确保所有依赖文件存在
3. 检查文件权限

### 图标质量问题
1. 确保源 SVG 文件设计正确
2. 验证 PNG 转换质量
3. 检查圆角半径是否符合 macOS 标准 (18.75%)
