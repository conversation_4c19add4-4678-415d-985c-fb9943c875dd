#!/bin/bash

echo "🎯 最终图标测试 - OFD Reader"
echo "================================"

# 显示当前图标规格
echo ""
echo "📐 当前图标规格:"
echo "- 圆角半径: 18.75% (192px)"
echo "- 尺寸: 1024x1024 源图标"
echo "- 格式: .icns (包含所有标准尺寸)"
echo "- 设计: 现代化 OFD 文档阅读器图标"

# 检查文件
echo ""
echo "📁 文件检查:"
if [ -f "assets/icons/icon_source.png" ]; then
    SIZE=$(sips -g pixelWidth assets/icons/icon_source.png | tail -1 | awk '{print $2}')
    echo "✅ 源图标: assets/icons/icon_source.png (${SIZE}x${SIZE})"
else
    echo "❌ 源图标文件缺失"
fi

if [ -f "assets/icons/AppIcon.icns" ]; then
    ICNS_SIZE=$(ls -lh assets/icons/AppIcon.icns | awk '{print $5}')
    echo "✅ 应用图标: assets/icons/AppIcon.icns ($ICNS_SIZE)"
else
    echo "❌ 应用图标文件缺失"
fi

if [ -d "dist/OFD Reader.app" ]; then
    echo "✅ 应用包: dist/OFD Reader.app"
    if [ -f "dist/OFD Reader.app/Contents/Resources/AppIcon.icns" ]; then
        echo "✅ 应用包中的图标文件存在"
    else
        echo "❌ 应用包中的图标文件缺失"
    fi
else
    echo "❌ 应用包缺失"
fi

echo ""
echo "🚀 启动应用进行图标测试..."
echo "请观察以下几点："
echo "1. 程序坞中的图标是否有正确的圆角"
echo "2. 图标尺寸是否与其他应用一致"
echo "3. 图标是否清晰，没有锯齿或模糊"
echo "4. 背景是否完全填充，没有白色边框"

echo ""
echo "按 Enter 键启动应用..."
read

# 启动应用
open "dist/OFD Reader.app"

echo ""
echo "✅ 应用已启动！"
echo ""
echo "🔍 请检查程序坞中的图标效果"
echo "如果图标仍有问题，请尝试："
echo "1. 重启 Dock: killall Dock"
echo "2. 重启 Finder: killall Finder"  
echo "3. 重启系统（最彻底的解决方案）"
echo ""
echo "按 Ctrl+C 退出应用"

# 等待用户输入
while true; do
    sleep 1
done
