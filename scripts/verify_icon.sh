#!/bin/bash

echo "🔍 验证 OFD Reader 图标配置..."

# 检查应用包
if [ ! -d "dist/OFD Reader.app" ]; then
    echo "❌ 未找到应用包: dist/OFD Reader.app"
    exit 1
fi

echo "✅ 应用包存在"

# 检查图标文件
ICON_PATH="dist/OFD Reader.app/Contents/Resources/AppIcon.icns"
SOURCE_ICON="assets/icons/AppIcon.icns"

if [ ! -f "$ICON_PATH" ]; then
    echo "❌ 未找到应用包中的图标文件: $ICON_PATH"
    exit 1
fi

if [ ! -f "$SOURCE_ICON" ]; then
    echo "⚠️  源图标文件不存在: $SOURCE_ICON"
fi

echo "✅ 图标文件存在"

# 显示图标文件详细信息
echo ""
echo "📊 图标文件信息:"
ls -lh "$ICON_PATH"
file "$ICON_PATH"

# 检查图标内容
echo ""
echo "🔍 图标内容分析:"
iconutil -l "$ICON_PATH" 2>/dev/null || echo "无法分析图标内容"

# 检查 Info.plist 配置
echo ""
echo "📋 Info.plist 图标配置:"
PLIST_PATH="dist/OFD Reader.app/Contents/Info.plist"

if grep -q "CFBundleIconFile" "$PLIST_PATH"; then
    echo "✅ CFBundleIconFile 已配置:"
    grep -A1 "CFBundleIconFile" "$PLIST_PATH" | grep -v "CFBundleIconFile"
else
    echo "❌ CFBundleIconFile 未配置"
fi

if grep -q "CFBundleIconName" "$PLIST_PATH"; then
    echo "✅ CFBundleIconName 已配置:"
    grep -A1 "CFBundleIconName" "$PLIST_PATH" | grep -v "CFBundleIconName"
else
    echo "⚠️  CFBundleIconName 未配置（可选）"
fi

# 检查应用分类
if grep -q "LSApplicationCategoryType" "$PLIST_PATH"; then
    echo "✅ 应用分类已配置:"
    grep -A1 "LSApplicationCategoryType" "$PLIST_PATH" | grep -v "LSApplicationCategoryType"
else
    echo "⚠️  应用分类未配置（可选）"
fi

echo ""
echo "🎯 图标设计规格验证:"
echo "- 圆角半径: 18.75% (192px for 1024x1024)"
echo "- 背景: 完全填充到边缘"
echo "- 尺寸: 支持 16x16 到 1024x1024 所有标准尺寸"

echo ""
echo "🚀 测试建议:"
echo "1. 在 Finder 中查看应用图标"
echo "2. 将应用拖到程序坞中测试"
echo "3. 检查不同尺寸下的显示效果"
echo "4. 如果图标仍有问题，可能需要重启系统"

echo ""
echo "✅ 图标验证完成！"
