#!/bin/bash

echo "🔍 测试 OFD Reader 图标显示效果..."

# 检查应用包是否存在
if [ ! -d "OFD Reader.app" ]; then
    echo "❌ 未找到 OFD Reader.app"
    exit 1
fi

# 检查图标文件是否存在
if [ ! -f "OFD Reader.app/Contents/Resources/AppIcon.icns" ]; then
    echo "❌ 未找到图标文件"
    exit 1
fi

echo "✅ 应用包和图标文件都存在"

# 显示图标文件信息
echo ""
echo "📊 图标文件信息:"
ls -lh "OFD Reader.app/Contents/Resources/AppIcon.icns"
file "OFD Reader.app/Contents/Resources/AppIcon.icns"

# 检查 Info.plist 中的图标配置
echo ""
echo "📋 Info.plist 图标配置:"
if grep -q "CFBundleIconFile" "OFD Reader.app/Contents/Info.plist"; then
    echo "✅ CFBundleIconFile 已配置"
    grep -A1 "CFBundleIconFile" "OFD Reader.app/Contents/Info.plist"
else
    echo "❌ CFBundleIconFile 未配置"
fi

# 启动应用以测试图标在程序坞中的显示
echo ""
echo "🚀 启动应用测试图标显示..."
echo "请检查程序坞中的图标是否正确显示（应该有圆角且无底色问题）"
echo "按 Ctrl+C 停止应用"

open "OFD Reader.app"

echo ""
echo "✅ 图标测试完成！"
echo "如果图标仍有问题，请检查："
echo "1. 图标是否有正确的圆角（229px 半径）"
echo "2. 背景是否完全填充到边缘"
echo "3. 是否需要重启系统以刷新图标缓存"
