//! 文件处理工具函数

use crate::core::error::OfdError;
use crate::Result;
use std::path::Path;
use log::debug;

/// 文件工具类
pub struct FileUtils;

impl FileUtils {
    /// 检查文件是否为 OFD 格式
    pub fn is_ofd_file<P: AsRef<Path>>(path: P) -> bool {
        path.as_ref()
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_lowercase() == "ofd")
            .unwrap_or(false)
    }

    /// 获取文件大小
    pub fn get_file_size<P: AsRef<Path>>(path: P) -> Result<u64> {
        let metadata = std::fs::metadata(path.as_ref())
            .map_err(OfdError::FileRead)?;
        Ok(metadata.len())
    }

    /// 检查文件是否可读
    pub fn is_readable<P: AsRef<Path>>(path: P) -> bool {
        std::fs::File::open(path.as_ref()).is_ok()
    }

    /// 格式化文件大小为人类可读的格式
    pub fn format_file_size(size: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
        let mut size = size as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", size as u64, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// 验证 OFD 文件
    pub fn validate_ofd_file<P: AsRef<Path>>(path: P) -> Result<()> {
        let path = path.as_ref();
        
        debug!("验证 OFD 文件: {:?}", path);
        
        if !path.exists() {
            return Err(OfdError::FileRead(
                std::io::Error::new(std::io::ErrorKind::NotFound, "文件不存在")
            ));
        }

        if !Self::is_ofd_file(path) {
            return Err(OfdError::InvalidFormat);
        }

        if !Self::is_readable(path) {
            return Err(OfdError::PermissionDenied);
        }

        Ok(())
    }
}
