//! 文件对话框处理模块

use crate::app::window::WindowManager;
use crate::core::error::OfdError;
use crate::Result;
use std::process::Command;
use log::{debug, error};
use slint::Weak;

/// 文件对话框管理器
pub struct FileDialogManager;

impl FileDialogManager {
    /// 打开 macOS 原生文件选择对话框
    pub fn open_native_dialog() -> Result<Option<String>> {
        debug!("打开 macOS 原生文件选择对话框");
        
        let output = Command::new("osascript")
            .arg("-e")
            .arg(r#"tell application "System Events" to return POSIX path of (choose file with prompt "选择 OFD 文件" of type {"ofd"})"#)
            .arg("-e")
            .arg("on error")
            .arg("-e")
            .arg("return \"\"")
            .arg("-e")
            .arg("end try")
            .output()
            .map_err(|e| {
                error!("执行 osascript 命令失败: {}", e);
                OfdError::FileDialog(format!("系统命令执行失败: {}", e))
            })?;

        let file_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
        
        if file_path.is_empty() {
            debug!("用户取消了文件选择");
            Ok(None)
        } else {
            debug!("用户选择了文件: {}", file_path);
            Ok(Some(file_path))
        }
    }
}

/// 打开文件对话框的便捷函数
/// 
/// # 参数
/// * `window_weak` - 主窗口的弱引用
/// 
/// # 返回值
/// * `Result<()>` - 操作结果
pub fn open_file_dialog(window_weak: Weak<crate::app::MainWindow>) -> Result<()> {
    match FileDialogManager::open_native_dialog()? {
        Some(file_path) => {
            WindowManager::load_ofd_file(window_weak, &file_path)
        }
        None => {
            debug!("用户取消了文件选择，无需进一步操作");
            Ok(())
        }
    }
}
