//! 窗口管理模块

use crate::core::ofd::OfdParser;
use crate::core::error::OfdError;
use crate::Result;
use slint::SharedString;
use log::{info, error, debug};

/// 窗口管理器
pub struct WindowManager;

impl WindowManager {
    /// 加载并解析 OFD 文件
    pub fn load_ofd_file(window: crate::app::MainWindow, file_path: &str) -> Result<()> {
        info!("开始加载 OFD 文件: {}", file_path);
        
        // 显示加载状态
        window.set_ofd_text(SharedString::from("正在加载文件..."));
        
        match OfdParser::parse_from_file(file_path) {
            Ok(document) => {
                debug!("文件解析成功");
                
                if document.is_empty() {
                    window.set_ofd_text(SharedString::from(
                        OfdError::EmptyContent.user_message()
                    ));
                } else {
                    let display_text = format!(
                        "文件：{}\n\n{}",
                        file_path,
                        document.content
                    );
                    window.set_ofd_text(SharedString::from(display_text));
                    info!("文件内容已成功显示在界面上");
                }
                Ok(())
            }
            Err(e) => {
                error!("解析 OFD 文件失败: {}", e);
                let error_message = format!(
                    "解析 OFD 文件失败：{}\n\n{}",
                    e,
                    e.user_message()
                );
                window.set_ofd_text(SharedString::from(error_message));
                Err(e)
            }
        }
    }

    /// 设置窗口文本内容
    pub fn set_text(window: &crate::app::MainWindow, text: &str) {
        window.set_ofd_text(SharedString::from(text));
    }

    /// 显示错误消息
    pub fn show_error(window: &crate::app::MainWindow, error: &OfdError) {
        let error_text = format!("错误：{}", error.user_message());
        Self::set_text(window, &error_text);
    }
}
