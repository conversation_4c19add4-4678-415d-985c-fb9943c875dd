//! 窗口管理模块

use crate::core::ofd::OfdParser;
use crate::core::error::OfdError;
use crate::Result;
use slint::{SharedString, Weak};
use log::{info, error, debug};
use std::path::Path;

/// 窗口管理器
pub struct WindowManager;

impl WindowManager {
    /// 加载并解析 OFD 文件
    /// 
    /// # 参数
    /// * `window_weak` - 主窗口的弱引用
    /// * `file_path` - OFD 文件路径
    /// 
    /// # 返回值
    /// * `Result<()>` - 操作结果
    pub fn load_ofd_file(window_weak: Weak<crate::app::MainWindow>, file_path: &str) -> Result<()> {
        info!("开始加载 OFD 文件: {}", file_path);
        
        // 尝试升级弱引用
        let Some(window) = window_weak.upgrade() else {
            error!("窗口引用已失效");
            return Err(OfdError::UiError("窗口引用已失效".to_string()));
        };
        
        // 更新状态栏
        window.set_status_text(SharedString::from("正在解析文件..."));
        let file_name = Path::new(file_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("未知文件");
        window.set_file_info(SharedString::from(format!("文件: {}", file_name)));
        
        // 显示加载状态
        window.set_ofd_text(SharedString::from("正在加载文件..."));
        
        match OfdParser::parse_from_file(file_path) {
            Ok(document) => {
                debug!("文件解析成功");
                window.set_status_text(SharedString::from("文件加载完成"));
                
                if document.is_empty() {
                    window.set_ofd_text(SharedString::from(
                        OfdError::EmptyContent.user_message()
                    ));
                    window.set_status_text(SharedString::from("文件内容为空"));
                } else {
                    let display_text = format!(
                        "文件：{}\n\n{}",
                        file_path,
                        document.content
                    );
                    window.set_ofd_text(SharedString::from(display_text));
                    window.set_status_text(SharedString::from("文件显示完成"));
                    info!("文件内容已成功显示在界面上");
                }
                Ok(())
            }
            Err(e) => {
                error!("解析 OFD 文件失败: {}", e);
                let error_message = format!(
                    "解析 OFD 文件失败：{}\n\n{}",
                    e,
                    e.user_message()
                );
                window.set_ofd_text(SharedString::from(error_message));
                window.set_status_text(SharedString::from("文件解析失败"));
                Err(e)
            }
        }
    }

    /// 设置窗口文本内容
    /// 
    /// # 参数
    /// * `window` - 主窗口引用
    /// * `text` - 要设置的文本内容
    pub fn set_text(window: &crate::app::MainWindow, text: &str) {
        window.set_ofd_text(SharedString::from(text));
    }

    /// 显示错误消息
    /// 
    /// # 参数
    /// * `window` - 主窗口引用
    /// * `error` - 错误对象
    pub fn show_error(window: &crate::app::MainWindow, error: &OfdError) {
        let error_text = format!("错误：{}", error.user_message());
        Self::set_text(window, &error_text);
        window.set_status_text(SharedString::from("发生错误"));
    }
}
