//! 应用层模块

pub mod window;
pub mod file_dialog;

use crate::core::error::OfdError;
use crate::Result;
use slint::SharedString;
use log::{info, error};

// 重新导出 Slint 生成的组件
slint::include_modules!();

/// 应用程序主类
pub struct App {
    window: MainWindow,
}

impl App {
    /// 创建新的应用程序实例
    pub fn new() -> Result<Self> {
        info!("初始化 OFD 阅读器应用");
        
        let window = MainWindow::new()
            .map_err(|e| OfdError::UiError(format!("创建主窗口失败: {}", e)))?;

        // 设置默认提示文本
        window.set_ofd_text(SharedString::from(
            "欢迎使用 OFD 阅读器！\n\n请点击'打开文件'按钮选择 OFD 文件进行阅读。"
        ));

        Ok(Self { window })
    }

    /// 设置文件打开回调
    pub fn setup_callbacks(&self) {
        let window_weak = self.window.as_weak();
        self.window.on_open_file(move || {
            if let Some(window) = window_weak.upgrade() {
                if let Err(e) = file_dialog::open_file_dialog(window) {
                    error!("打开文件对话框失败: {}", e);
                }
            }
        });
    }

    /// 运行应用程序
    pub fn run(self) -> Result<()> {
        info!("启动应用程序主循环");
        self.window.run()
            .map_err(|e| OfdError::UiError(format!("应用程序运行失败: {}", e)))
    }

    /// 获取窗口引用
    pub fn window(&self) -> &MainWindow {
        &self.window
    }
}
