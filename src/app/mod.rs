//! 应用层模块

pub mod window;
pub mod file_dialog;

use crate::core::error::OfdError;
use crate::Result;
use slint::SharedString;
use log::{info, error};

// 重新导出 Slint 生成的组件
slint::include_modules!();

/// 应用程序主类
pub struct App {
    window: MainWindow,
}

impl App {
    /// 创建新的应用程序实例
    pub fn new() -> Result<Self> {
        info!("初始化 OFD 阅读器应用");
        
        let window = MainWindow::new()
            .map_err(|e| OfdError::UiError(format!("创建主窗口失败: {}", e)))?;

        // 设置默认提示文本
        window.set_ofd_text(SharedString::from(
            "欢迎使用 OFD 阅读器！\n\n请点击'打开文件'按钮选择 OFD 文件进行阅读。"
        ));
        
        // 设置初始状态
        window.set_status_text(SharedString::from("就绪"));
        window.set_file_info(SharedString::from(""));

        Ok(Self { window })
    }

    /// 设置所有回调函数
    pub fn setup_callbacks(&self) {
        let window_weak = self.window.as_weak();
        
        // 文件打开回调
        self.window.on_open_file(move || {
            if let Some(window) = window_weak.upgrade() {
                window.set_status_text(SharedString::from("正在打开文件..."));
                let window_weak_inner = window.as_weak();
                if let Err(e) = file_dialog::open_file_dialog(window_weak_inner) {
                    error!("打开文件对话框失败: {}", e);
                    // 重新获取窗口引用来设置错误状态
                    if let Some(window) = window_weak.upgrade() {
                        window.set_status_text(SharedString::from("打开文件失败"));
                    }
                }
            }
        });
        
        // 最近文件回调
        let window_weak = self.window.as_weak();
        self.window.on_file_recent(move |path| {
            if let Some(window) = window_weak.upgrade() {
                let path_str = path.to_string();
                info!("打开最近文件: {}", path_str);
                window.set_status_text(SharedString::from("正在加载最近文件..."));
                let window_weak_inner = window.as_weak();
                if let Err(e) = window::WindowManager::load_ofd_file(window_weak_inner, &path_str) {
                    error!("加载最近文件失败: {}", e);
                    // 重新获取窗口引用来设置错误状态
                    if let Some(window) = window_weak.upgrade() {
                        window.set_status_text(SharedString::from("加载文件失败"));
                    }
                }
            }
        });
        
        // 帮助关于回调
        let window_weak = self.window.as_weak();
        self.window.on_help_about(move || {
            if let Some(window) = window_weak.upgrade() {
                let about_text = "OFD Reader v1.0\n\n一个现代化的 OFD 文档阅读器\n\n使用 Rust + Slint 构建\n\nCopyright © 2024 NJSTJ0328";
                window.set_ofd_text(SharedString::from(about_text));
                window.set_status_text(SharedString::from("显示关于信息"));
            }
        });
    }

    /// 运行应用程序
    pub fn run(self) -> Result<()> {
        info!("启动应用程序主循环");
        self.window.run()
            .map_err(|e| OfdError::UiError(format!("应用程序运行失败: {}", e)))
    }

    /// 获取窗口引用
    pub fn window(&self) -> &MainWindow {
        &self.window
    }
}
