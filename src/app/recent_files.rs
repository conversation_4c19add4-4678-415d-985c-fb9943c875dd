//! 最近文件管理模块

use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use crate::Result;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RecentFile {
    pub path: PathBuf,
    pub last_opened: chrono::DateTime<chrono::Utc>,
    pub display_name: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct RecentFiles {
    files: Vec<RecentFile>,
    max_count: usize,
}

impl RecentFiles {
    /// 创建新的最近文件管理器
    pub fn new(max_count: usize) -> Self {
        Self {
            files: Vec::new(),
            max_count,
        }
    }
    
    /// 添加最近打开的文件
    pub fn add_file<P: AsRef<Path>>(&mut self, path: P) {
        let path = path.as_ref().to_path_buf();
        let display_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("未知文件")
            .to_string();
        
        let recent_file = RecentFile {
            path: path.clone(),
            last_opened: chrono::Utc::now(),
            display_name,
        };
        
        // 移除已存在的相同文件
        self.files.retain(|f| f.path != path);
        
        // 添加到开头
        self.files.insert(0, recent_file);
        
        // 保持最大数量限制
        if self.files.len() > self.max_count {
            self.files.truncate(self.max_count);
        }
    }
    
    /// 获取最近文件列表
    pub fn get_files(&self) -> &[RecentFile] {
        &self.files
    }
    
    /// 清理不存在的文件
    pub fn cleanup(&mut self) {
        self.files.retain(|f| f.path.exists());
    }
}