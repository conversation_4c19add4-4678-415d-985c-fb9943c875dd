//! 错误处理模块

use thiserror::Error;

/// OFD 阅读器的错误类型
#[derive(Debug, Error)]
pub enum OfdError {
    #[error("文件读取失败: {0}")]
    FileRead(#[from] std::io::Error),
    
    #[error("ZIP 文件解析失败: {0}")]
    ZipParse(#[from] zip::result::ZipError),
    
    #[error("XML 解析失败: {0}")]
    XmlParse(#[from] quick_xml::Error),
    
    #[error("不是有效的 OFD 文件")]
    InvalidFormat,
    
    #[error("文件内容为空")]
    EmptyContent,
    
    #[error("文件对话框操作失败: {0}")]
    FileDialog(String),
    
    #[error("UI 操作失败: {0}")]
    UiError(String),
    
    #[error("系统权限不足")]
    PermissionDenied,
}

impl OfdError {
    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            OfdError::FileRead(_) => "读取文件失败，请检查文件是否存在且有读取权限。".to_string(),
            OfdError::ZipParse(_) => "文件格式错误，请确保选择的是有效的 OFD 文件。".to_string(),
            OfdError::XmlParse(_) => "文件内容解析失败，文件可能已损坏。".to_string(),
            OfdError::InvalidFormat => "不是有效的 OFD 文件格式。".to_string(),
            OfdError::EmptyContent => "文件解析成功，但未找到文本内容。\n\n这可能是因为：\n1. OFD 文件主要包含图像内容\n2. 文件格式不标准\n3. 文本内容位于不同的结构中".to_string(),
            OfdError::FileDialog(msg) => format!("打开文件对话框失败：{}", msg),
            OfdError::UiError(msg) => format!("界面操作失败：{}", msg),
            OfdError::PermissionDenied => "系统权限不足，请检查应用程序权限设置。".to_string(),
        }
    }
}
