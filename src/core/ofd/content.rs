//! OFD 内容提取器

use crate::core::error::OfdError;
use crate::Result;
use zip::ZipArchive;
use quick_xml::{Reader, events::Event};
use std::io::Read;
use log::{debug, trace};

/// 内容提取器
pub struct ContentExtractor;

impl ContentExtractor {
    /// 从 ZIP 归档中提取文本内容
    pub fn extract_text_content<R: Read + std::io::Seek>(
        zip: &mut ZipArchive<R>
    ) -> Result<String> {
        let mut result = String::new();
        let mut page_count = 0;

        for i in 0..zip.len() {
            let mut file = zip.by_index(i)
                .map_err(OfdError::ZipParse)?;
            
            let name = file.name().to_string();
            debug!("检查文件: {}", name);
            
            // 查找页面内容文件
            if name.contains("Pages/") && name.ends_with("Content.xml") {
                debug!("找到页面内容文件: {}", name);
                page_count += 1;
                
                let mut xml = String::new();
                file.read_to_string(&mut xml)
                    .map_err(OfdError::FileRead)?;
                
                let page_text = Self::extract_text_from_xml(&xml)?;
                if !page_text.is_empty() {
                    if !result.is_empty() {
                        result.push_str("\n\n--- 第 ");
                        result.push_str(&page_count.to_string());
                        result.push_str(" 页 ---\n\n");
                    }
                    result.push_str(&page_text);
                }
            }
        }

        debug!("总共处理了 {} 个页面", page_count);
        Ok(result)
    }

    /// 从 XML 内容中提取文本
    fn extract_text_from_xml(xml: &str) -> Result<String> {
        let mut reader = Reader::from_str(xml);
        reader.trim_text(true);
        let mut buf = Vec::new();
        let mut result = String::new();

        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Text(text)) => {
                    let content = text.unescape()
                        .map_err(OfdError::XmlParse)?;
                    
                    let trimmed = content.trim();
                    if !trimmed.is_empty() {
                        trace!("提取到文本: {}", trimmed);
                        result.push_str(trimmed);
                        result.push('\n');
                    }
                }
                Ok(Event::Eof) => break,
                Err(e) => return Err(OfdError::XmlParse(e)),
                _ => {}
            }
            buf.clear();
        }

        Ok(result)
    }
}
