//! OFD 文件解析器

use crate::core::error::OfdError;
use crate::core::ofd::{OfdDocument, ContentExtractor};
use crate::Result;
use zip::ZipArchive;
use std::io::Cursor;
use log::{debug, info, warn};

/// OFD 解析器
pub struct OfdParser;

impl OfdParser {
    /// 从字节数据解析 OFD 文档
    pub fn parse_from_bytes(data: &[u8]) -> Result<OfdDocument> {
        info!("开始解析 OFD 文件，数据大小: {} 字节", data.len());
        
        let mut zip = ZipArchive::new(Cursor::new(data))
            .map_err(OfdError::ZipParse)?;
        
        debug!("ZIP 文件包含 {} 个条目", zip.len());
        
        let content = ContentExtractor::extract_text_content(&mut zip)?;
        
        if content.trim().is_empty() {
            warn!("未找到文本内容");
            return Err(OfdError::EmptyContent);
        }
        
        info!("成功提取文本内容，长度: {} 字符", content.len());
        
        Ok(OfdDocument::new(content))
    }
    
    /// 从文件路径解析 OFD 文档
    pub fn parse_from_file(file_path: &str) -> Result<OfdDocument> {
        info!("从文件解析 OFD: {}", file_path);
        
        let data = std::fs::read(file_path)
            .map_err(OfdError::FileRead)?;
        
        let mut document = Self::parse_from_bytes(&data)?;
        document.file_path = Some(file_path.to_string());
        
        Ok(document)
    }
}

/// 兼容性函数，保持与原有代码的兼容性
pub fn parse_ofd_text(data: &[u8]) -> Result<String> {
    let document = OfdParser::parse_from_bytes(data)?;
    Ok(document.content)
}
