//! OFD 文档模型

// 这些导入将在未来的功能中使用
// use crate::core::error::OfdError;
// use crate::Result;

/// OFD 文档结构
#[derive(Debug, <PERSON><PERSON>)]
pub struct OfdDocument {
    /// 文档内容
    pub content: String,
    /// 文档路径
    pub file_path: Option<String>,
    /// 页面数量
    pub page_count: usize,
    /// 文档元数据
    pub metadata: DocumentMetadata,
}

/// 文档元数据
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct DocumentMetadata {
    /// 文档标题
    pub title: Option<String>,
    /// 作者
    pub author: Option<String>,
    /// 创建时间
    pub created: Option<String>,
    /// 修改时间
    pub modified: Option<String>,
}

impl OfdDocument {
    /// 创建新的 OFD 文档
    pub fn new(content: String) -> Self {
        Self {
            content,
            file_path: None,
            page_count: 1,
            metadata: DocumentMetadata::default(),
        }
    }

    /// 设置文件路径
    pub fn with_file_path(mut self, path: String) -> Self {
        self.file_path = Some(path);
        self
    }

    /// 设置页面数量
    pub fn with_page_count(mut self, count: usize) -> Self {
        self.page_count = count;
        self
    }

    /// 设置元数据
    pub fn with_metadata(mut self, metadata: DocumentMetadata) -> Self {
        self.metadata = metadata;
        self
    }

    /// 检查文档是否为空
    pub fn is_empty(&self) -> bool {
        self.content.trim().is_empty()
    }

    /// 获取文档摘要
    pub fn summary(&self) -> String {
        let content_preview = if self.content.len() > 100 {
            format!("{}...", &self.content[..100])
        } else {
            self.content.clone()
        };

        format!(
            "文档路径: {}\n页面数: {}\n内容预览: {}",
            self.file_path.as_deref().unwrap_or("未知"),
            self.page_count,
            content_preview
        )
    }
}
