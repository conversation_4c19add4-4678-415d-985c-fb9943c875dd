//! OFD 文档缓存模块

use std::collections::HashMap;
use std::path::PathBuf;
use std::time::{Duration, Instant};
use crate::core::ofd::OfdDocument;

/// 文档缓存项
#[derive(Debug, Clone)]
struct CacheItem {
    document: OfdDocument,
    last_accessed: Instant,
    file_modified: std::time::SystemTime,
}

/// 文档缓存管理器
pub struct DocumentCache {
    cache: HashMap<PathBuf, CacheItem>,
    max_size: usize,
    ttl: Duration,
}

impl DocumentCache {
    /// 创建新的缓存管理器
    pub fn new(max_size: usize, ttl: Duration) -> Self {
        Self {
            cache: HashMap::new(),
            max_size,
            ttl,
        }
    }
    
    /// 获取缓存的文档
    pub fn get(&mut self, path: &PathBuf) -> Option<&OfdDocument> {
        // 检查文件是否被修改
        if let Ok(metadata) = std::fs::metadata(path) {
            if let Ok(modified) = metadata.modified() {
                if let Some(item) = self.cache.get_mut(path) {
                    if item.file_modified < modified {
                        // 文件已被修改，移除缓存
                        self.cache.remove(path);
                        return None;
                    }
                    
                    // 更新访问时间
                    item.last_accessed = Instant::now();
                    return Some(&item.document);
                }
            }
        }
        
        None
    }
    
    /// 添加文档到缓存
    pub fn insert(&mut self, path: PathBuf, document: OfdDocument) {
        // 清理过期缓存
        self.cleanup();
        
        // 如果缓存已满，移除最旧的项
        if self.cache.len() >= self.max_size {
            self.evict_oldest();
        }
        
        if let Ok(metadata) = std::fs::metadata(&path) {
            if let Ok(modified) = metadata.modified() {
                let item = CacheItem {
                    document,
                    last_accessed: Instant::now(),
                    file_modified: modified,
                };
                
                self.cache.insert(path, item);
            }
        }
    }
    
    /// 清理过期缓存
    fn cleanup(&mut self) {
        let now = Instant::now();
        self.cache.retain(|_, item| {
            now.duration_since(item.last_accessed) < self.ttl
        });
    }
    
    /// 移除最旧的缓存项
    fn evict_oldest(&mut self) {
        if let Some((oldest_path, _)) = self.cache
            .iter()
            .min_by_key(|(_, item)| item.last_accessed)
            .map(|(path, item)| (path.clone(), item.last_accessed))
        {
            self.cache.remove(&oldest_path);
        }
    }
}