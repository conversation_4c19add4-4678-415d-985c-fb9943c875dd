//! 配置管理模块

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    /// 窗口配置
    pub window: WindowConfig,
    /// 文件处理配置
    pub file: FileConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WindowConfig {
    pub width: u32,
    pub height: u32,
    pub remember_size: bool,
    pub remember_position: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileConfig {
    pub recent_files_limit: usize,
    pub auto_save_position: bool,
    pub default_zoom: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_output: bool,
    pub console_output: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            window: WindowConfig {
                width: 900,
                height: 700,
                remember_size: true,
                remember_position: true,
            },
            file: FileConfig {
                recent_files_limit: 10,
                auto_save_position: true,
                default_zoom: 1.0,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_output: false,
                console_output: true,
            },
        }
    }
}

/// 配置管理器
pub struct ConfigManager;

impl ConfigManager {
    /// 加载配置文件
    pub fn load() -> crate::Result<AppConfig> {
        let config_path = Self::config_path()?;
        
        if config_path.exists() {
            let content = std::fs::read_to_string(&config_path)?;
            let config: AppConfig = toml::from_str(&content)
                .map_err(|e| crate::core::error::OfdError::ConfigError(format!("配置文件解析失败: {}", e)))?;
            Ok(config)
        } else {
            let default_config = AppConfig::default();
            Self::save(&default_config)?;
            Ok(default_config)
        }
    }
    
    /// 保存配置文件
    pub fn save(config: &AppConfig) -> crate::Result<()> {
        let config_path = Self::config_path()?;
        
        if let Some(parent) = config_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        let content = toml::to_string_pretty(config)
            .map_err(|e| crate::core::error::OfdError::ConfigError(format!("配置序列化失败: {}", e)))?;
        
        std::fs::write(&config_path, content)?;
        Ok(())
    }
    
    /// 获取配置文件路径
    fn config_path() -> crate::Result<PathBuf> {
        let mut path = dirs::config_dir()
            .ok_or_else(|| crate::core::error::OfdError::ConfigError("无法获取配置目录".to_string()))?;
        path.push("ofdx-reader");
        path.push("config.toml");
        Ok(path)
    }
}