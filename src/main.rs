#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use ofdx_reader::App;
use log::error;

/// 主函数：启动 OFD 阅读器应用
fn main() {
    // 初始化日志
    env_logger::init();

    // 创建并运行应用
    match App::new() {
        Ok(app) => {
            app.setup_callbacks();
            if let Err(e) = app.run() {
                error!("应用程序运行失败: {}", e);
                std::process::exit(1);
            }
        }
        Err(e) => {
            error!("应用程序初始化失败: {}", e);
            std::process::exit(1);
        }
    }
}