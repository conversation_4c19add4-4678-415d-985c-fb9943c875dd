<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主背景渐变 - 现代蓝色系 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#5856D6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AF52DE;stop-opacity:1" />
    </linearGradient>

    <!-- 玻璃高光效果 -->
    <linearGradient id="glassHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- 文档渐变 -->
    <linearGradient id="docGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.98" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.95" />
    </linearGradient>

    <!-- 头部渐变 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF3B30;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9500;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="docShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>

  <!-- macOS 标准圆角背景 - 18.75% 圆角半径 = 192px -->
  <rect x="0" y="0" width="1024" height="1024" rx="192" ry="192"
        fill="url(#mainGradient)"/>

  <!-- 玻璃高光层 -->
  <rect x="0" y="0" width="1024" height="1024" rx="192" ry="192"
        fill="url(#glassHighlight)"/>

  <!-- 文档主体 - 更大更居中 -->
  <rect x="140" y="120" width="744" height="784" rx="45" ry="45"
        fill="url(#docGradient)"
        filter="url(#docShadow)"
        stroke="rgba(255,255,255,0.5)" stroke-width="3"/>

  <!-- 文档头部 -->
  <rect x="140" y="120" width="744" height="160" rx="45" ry="45"
        fill="url(#headerGradient)"/>
  <rect x="140" y="235" width="744" height="45"
        fill="url(#headerGradient)"/>

  <!-- OFD 文字 - 更大更突出 -->
  <text x="512" y="230"
        font-family="-apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif"
        font-size="100"
        font-weight="800"
        text-anchor="middle"
        fill="#ffffff"
        style="letter-spacing: 6px;">OFD</text>

  <!-- 文档内容线条 - 更大更清晰 -->
  <rect x="220" y="340" width="584" height="24" rx="12" fill="rgba(0,0,0,0.8)"/>
  <rect x="220" y="385" width="480" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="420" width="520" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="455" width="440" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="490" width="500" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="525" width="460" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="560" width="540" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="595" width="420" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="630" width="480" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>
  <rect x="220" y="665" width="400" height="20" rx="10" fill="rgba(0,0,0,0.6)"/>

  <!-- 现代化阅读图标 - 更大更突出 -->
  <g transform="translate(700, 750)">
    <!-- 眼睛外框 -->
    <ellipse cx="0" cy="0" rx="100" ry="65"
             fill="rgba(255,255,255,0.98)"
             stroke="rgba(0,122,255,0.9)"
             stroke-width="4"/>

    <!-- 眼球 -->
    <circle cx="0" cy="0" r="42" fill="#007AFF"/>

    <!-- 瞳孔 -->
    <circle cx="0" cy="0" r="22" fill="#000000"/>

    <!-- 高光点 -->
    <circle cx="8" cy="-8" r="8" fill="#ffffff" opacity="0.95"/>
    <circle cx="-4" cy="4" r="3" fill="#ffffff" opacity="0.7"/>
  </g>

  <!-- 顶部玻璃反光效果 -->
  <ellipse cx="512" cy="180" rx="350" ry="90"
           fill="rgba(255,255,255,0.35)"
           opacity="0.9"/>

</svg>
