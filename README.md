# OFD Reader

一个现代化的 OFD (Open Fixed-layout Document) 文档阅读器，使用 Rust 和 Slint 构建。

## 功能特性

- 🔍 **OFD 文档解析**：支持标准 OFD 格式文档
- 🖥️ **原生 GUI**：使用 Slint 构建的现代化界面
- 📁 **文件管理**：最近文件列表，拖拽支持
- ⚡ **高性能**：文档缓存，异步加载
- 🔧 **可配置**：丰富的配置选项
- 🍎 **macOS 优化**：原生文件对话框，应用包支持

## 快速开始

### 开发环境

```bash
# 克隆项目
git clone https://github.com/njstj0328/ofdx-reader.git
cd ofdx-reader

# 安装依赖并运行
cargo run
```

### 构建 macOS 应用包

```bash
# 生成应用图标
./scripts/create_icon.sh

# 构建完整的 .app 包
./scripts/build_app.sh

# 验证构建结果
./scripts/verify_icon.sh
```

## 📁 项目结构

```
ofdx-reader/
├── src/                    # 源代码
│   ├── app/               # 应用层
│   ├── core/              # 核心功能
│   ├── config/            # 配置管理
│   ├── ui/                # UI 组件
│   └── utils/             # 工具函数
├── slint-ui/              # Slint UI 定义
│   ├── components/        # UI 组件
│   └── main.slint         # 主界面
├── assets/                # 资源文件
│   └── icons/             # 应用图标
│       ├── icon_source.svg    # SVG 源文件
│       ├── icon_source.png    # PNG 源文件
│       └── AppIcon.icns       # macOS 图标
├── scripts/               # 构建和工具脚本
│   ├── build_app.sh       # 构建应用包
│   ├── create_icon.sh     # 生成图标
│   ├── verify_icon.sh     # 验证图标
│   ├── test_icon.sh       # 测试图标
│   ├── final_icon_test.sh # 最终测试
│   └── README.md          # 脚本使用说明
├── tests/                 # 测试文件
├── Info.plist            # macOS 应用信息
└── README.md             # 项目说明
```

## 🛠️ 构建脚本

本项目提供了完整的构建和测试脚本，详细说明请参考 [scripts/README.md](scripts/README.md)。

### 主要脚本

| 脚本 | 用途 | 说明 |
|------|------|------|
| `scripts/build_app.sh` | 构建应用包 | 编译并创建完整的 macOS .app 包 |
| `scripts/create_icon.sh` | 生成图标 | 从 SVG 源文件生成所有尺寸的应用图标 |
| `scripts/verify_icon.sh` | 验证配置 | 检查图标文件和应用配置 |
| `scripts/final_icon_test.sh` | 最终测试 | 启动应用进行完整的图标测试 |

### 快速构建

使用 Makefile（推荐）：
```bash
# 查看所有可用命令
make help

# 开发模式运行
make dev

# 构建完整应用包
make build

# 验证构建结果
make verify

# 安装到 Applications 目录
make install
```

或直接使用脚本：
```bash
# 完整构建流程
./scripts/create_icon.sh    # 生成图标
./scripts/build_app.sh      # 构建应用
./scripts/verify_icon.sh    # 验证结果
```
