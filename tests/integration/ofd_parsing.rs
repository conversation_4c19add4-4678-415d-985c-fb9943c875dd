//! OFD 解析集成测试

use ofdx_reader::core::ofd::OfdParser;

#[test]
fn test_parse_invalid_data() {
    let invalid_data = b"not a zip file";
    let result = OfdParser::parse_from_bytes(invalid_data);
    assert!(result.is_err());
}

#[test]
fn test_parse_empty_data() {
    let empty_data = b"";
    let result = OfdParser::parse_from_bytes(empty_data);
    assert!(result.is_err());
}

// 注意：这里需要真实的 OFD 文件来进行完整测试
// 可以在 tests/fixtures/ 目录下放置测试用的 OFD 文件
