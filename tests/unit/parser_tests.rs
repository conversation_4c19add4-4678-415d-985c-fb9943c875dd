//! 解析器单元测试

use ofdx_reader::core::ofd::document::{OfdDocument, DocumentMetadata};

#[test]
fn test_ofd_document_creation() {
    let content = "测试内容".to_string();
    let doc = OfdDocument::new(content.clone());
    
    assert_eq!(doc.content, content);
    assert_eq!(doc.file_path, None);
    assert_eq!(doc.page_count, 1);
}

#[test]
fn test_ofd_document_with_file_path() {
    let content = "测试内容".to_string();
    let path = "/path/to/test.ofd".to_string();
    let doc = OfdDocument::new(content.clone())
        .with_file_path(path.clone());
    
    assert_eq!(doc.file_path, Some(path));
}

#[test]
fn test_ofd_document_is_empty() {
    let empty_doc = OfdDocument::new("".to_string());
    let whitespace_doc = OfdDocument::new("   \n\t  ".to_string());
    let content_doc = OfdDocument::new("有内容".to_string());
    
    assert!(empty_doc.is_empty());
    assert!(whitespace_doc.is_empty());
    assert!(!content_doc.is_empty());
}

#[test]
fn test_document_metadata() {
    let metadata = DocumentMetadata {
        title: Some("测试文档".to_string()),
        author: Some("测试作者".to_string()),
        created: Some("2024-01-01".to_string()),
        modified: Some("2024-01-02".to_string()),
    };
    
    let doc = OfdDocument::new("内容".to_string())
        .with_metadata(metadata.clone());
    
    assert_eq!(doc.metadata.title, metadata.title);
    assert_eq!(doc.metadata.author, metadata.author);
}
