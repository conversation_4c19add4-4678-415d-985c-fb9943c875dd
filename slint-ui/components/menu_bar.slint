import { <PERSON>ton, VerticalBox, HorizontalBox } from "std-widgets.slint";

// 菜单栏组件
export component MenuBar inherits Rectangle {
    callback file_open();
    callback file_recent(string);
    callback help_about();
    
    height: 30px;
    background: #f0f0f0;
    
    VerticalBox {
        // 主菜单区域
        Rectangle {
            height: parent.height - 1px;
            background: #f0f0f0;
            
            HorizontalBox {
                padding: 5px;
                spacing: 15px;
                
                // 文件菜单
                Rectangle {
                    width: 60px;
                    height: parent.height;
                    background: file_touch.has-hover ? #e0e0e0 : transparent;
                    
                    Text {
                        text: "文件";
                        font-size: 14px;
                        color: #333;
                        vertical-alignment: center;
                        horizontal-alignment: center;
                    }
                    
                    file_touch := TouchArea {
                        clicked => {
                            root.file_open();
                        }
                        
                        mouse-cursor: pointer;
                    }
                }
                
                // 帮助菜单
                Rectangle {
                    width: 60px;
                    height: parent.height;
                    background: help_touch.has-hover ? #e0e0e0 : transparent;
                    
                    Text {
                        text: "帮助";
                        font-size: 14px;
                        color: #333;
                        vertical-alignment: center;
                        horizontal-alignment: center;
                    }
                    
                    help_touch := TouchArea {
                        clicked => {
                            root.help_about();
                        }
                        
                        mouse-cursor: pointer;
                    }
                }
                
                Rectangle {
                    // 占位符，推送菜单到左侧
                }
            }
        }
        
        // 底部边框线
        Rectangle {
            height: 1px;
            background: #d0d0d0;
        }
    }
}