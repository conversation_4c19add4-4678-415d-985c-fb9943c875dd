import { VerticalBox, HorizontalBox } from "std-widgets.slint";

// 状态栏组件
export component StatusBar inherits Rectangle {
    in property <string> status_text: "就绪";
    in property <string> file_info: "";
    
    height: 25px;
    background: #e8e8e8;
    
    VerticalBox {
        // 顶部边框线
        Rectangle {
            height: 1px;
            background: #d0d0d0;
        }
        
        // 主状态栏区域
        Rectangle {
            height: parent.height - 1px;
            background: #e8e8e8;
            
            HorizontalBox {
                padding: 8px;
                spacing: 10px;
                alignment: center;
                
                // 状态文本
                Text {
                    text: root.status_text;
                    font-size: 12px;
                    color: #555;
                    vertical-alignment: center;
                }
                
                // 分隔符
                Rectangle {
                    width: 1px;
                    height: 15px;
                    background: #ccc;
                }
                
                // 文件信息
                Text {
                    text: root.file_info;
                    font-size: 12px;
                    color: #666;
                    vertical-alignment: center;
                }
                
                Rectangle {
                    // 占位符，推送内容到左侧
                }
                
                // 右侧信息
                Text {
                    text: "OFD Reader v1.0";
                    font-size: 11px;
                    color: #888;
                    vertical-alignment: center;
                }
            }
        }
    }
}