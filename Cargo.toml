[package]
name = "ofdx-reader"
version = "0.1.0"
edition = "2021"
authors = ["NJSTJ0328"]
description = "一个现代化的 OFD 文档阅读器"
license = "MIT"
repository = "https://github.com/njstj0328/ofdx-reader"
keywords = ["ofd", "document", "reader", "gui"]
categories = ["multimedia", "text-processing"]

# macOS 特定配置
[package.metadata.bundle]
name = "OFDReader"
identifier = "com.power.ofdx-reader"
version = "1.0.0"
copyright = "Copyright © 2024 NJSTJ0328"
category = "Productivity"
short_description = "OFD文档阅读器"
long_description = "一个用于阅读OFD格式文档的应用程序"
build = "build.rs"

[dependencies]
slint = "1.12.1"
zip = "0.6.6"
quick-xml = "0.31.0"
thiserror = "1.0.69"
anyhow = "1.0.98"
log = "0.4.27"
env_logger = "0.10.2"
# 新增依赖
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"
chrono = { version = "0.4", features = ["serde"] }
dirs = "5.0"
tokio = { version = "1.0", features = ["rt-multi-thread", "fs"], optional = true }
once_cell = "1.19"

[build-dependencies]
slint-build = "1.12.1"

[features]
default = ["slint/backend-winit"]
async = ["tokio"]

# 性能优化配置
[profile.release]
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 1
