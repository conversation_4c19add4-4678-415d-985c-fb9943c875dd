# macOS 平台配置

本目录包含 OFDReader 在 macOS 平台上的特定配置文件和资源。

## 📁 文件说明

### `Info.plist`
**用途**: macOS 应用包的核心配置文件

**主要配置项**:
- `CFBundleName`: 应用显示名称
- `CFBundleIdentifier`: 应用唯一标识符
- `CFBundleVersion`: 应用版本号
- `CFBundleShortVersionString`: 用户可见版本号
- `CFBundleExecutable`: 可执行文件名
- `CFBundleIconFile`: 图标文件名
- `CFBundleIconName`: 图标名称
- `LSApplicationCategoryType`: 应用分类
- `NSHighResolutionCapable`: 支持高分辨率显示

**编辑注意事项**:
- 使用标准的 XML plist 格式
- 确保所有必需的键值对都存在
- 版本号应与 Cargo.toml 中的版本保持一致
- 图标文件路径应与实际文件位置匹配

## 🔧 配置管理

### 版本同步
确保以下文件中的版本号保持一致：
- `Cargo.toml` - Rust 项目版本
- `platform/macos/Info.plist` - macOS 应用版本
- Git 标签版本

### 图标配置
Info.plist 中的图标配置应与以下文件匹配：
- `assets/icons/AppIcon.icns` - 实际图标文件
- `CFBundleIconFile` 和 `CFBundleIconName` 配置项

### 应用标识符
`CFBundleIdentifier` 应该：
- 使用反向域名格式 (如: com.company.appname)
- 在整个应用生命周期中保持不变
- 在 App Store 中唯一

## 🚀 构建集成

构建脚本会自动：
1. 从此目录复制 `Info.plist` 到应用包
2. 验证配置文件的完整性
3. 确保图标文件正确关联

## 📋 最佳实践

### 版本管理
```bash
# 更新版本时，同时更新多个文件
# 1. 更新 Cargo.toml
# 2. 更新 platform/macos/Info.plist
# 3. 创建对应的 Git 标签
```

### 配置验证
使用以下命令验证 plist 文件格式：
```bash
plutil -lint platform/macos/Info.plist
```

### 权限配置
如果应用需要特殊权限，在 Info.plist 中添加相应的使用说明：
- `NSDocumentsFolderUsageDescription` - 文档文件夹访问
- `NSDesktopFolderUsageDescription` - 桌面文件夹访问
- 等等

## 🔍 故障排除

### 应用无法启动
1. 检查 `CFBundleExecutable` 是否指向正确的可执行文件
2. 验证 Info.plist 格式是否正确
3. 确保所有必需的配置项都存在

### 图标不显示
1. 检查 `CFBundleIconFile` 配置
2. 确保图标文件存在于 Resources 目录
3. 验证图标文件格式是否正确

### 版本信息错误
1. 同步 Cargo.toml 和 Info.plist 中的版本号
2. 清理并重新构建应用包
3. 检查系统缓存是否需要清理

## 📚 相关文档

- [Apple Developer Documentation - Information Property List](https://developer.apple.com/documentation/bundleresources/information_property_list)
- [macOS App Bundle Structure](https://developer.apple.com/library/archive/documentation/CoreFoundation/Conceptual/CFBundles/BundleTypes/BundleTypes.html)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
